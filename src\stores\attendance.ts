import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AttendanceItem } from '@/types/attendance'

export const useAttendanceStore = defineStore('attendance', () => {
  // 当前选中的考勤记录
  const currentAttendance = ref<AttendanceItem | null>(null)

  // 设置当前考勤记录
  const setCurrentAttendance = (attendance: AttendanceItem) => {
    currentAttendance.value = attendance
  }

  // 获取当前考勤记录
  const getCurrentAttendance = () => {
    return currentAttendance.value
  }

  // 清除当前考勤记录
  const clearCurrentAttendance = () => {
    currentAttendance.value = null
  }

  return {
    currentAttendance,
    setCurrentAttendance,
    getCurrentAttendance,
    clearCurrentAttendance,
  }
})
