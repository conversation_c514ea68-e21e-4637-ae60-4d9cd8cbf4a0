<route lang="json5">
{
  style: {
    navigationBarTitleText: '考勤详情',
  },
}
</route>

<template>
  <FormWithApproval
    :code="'xsfcqkqbksq'"
    :id="attendanceInfo.apply?.id"
    :show-workflow="!!attendanceInfo.apply"
  >
    <template #form-content>
      <view class="attendance-detail-page">
        <view class="detail-container">
          <!-- 基本信息 -->
          <view class="info-card">
            <view class="card-header">
              <text class="card-title">考勤信息</text>
            </view>
            <view class="card-content">
              <view class="info-row">
                <text class="info-label">学年学期</text>
                <text class="info-value">
                  {{ attendanceInfo.studyYear }} ({{ attendanceInfo.studyTerm }})
                </text>
              </view>
              <view class="info-row">
                <text class="info-label">课程任务</text>
                <text class="info-value">{{ attendanceInfo.courseName }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">授课教师</text>
                <text class="info-value">{{ attendanceInfo.teacherName }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">考勤日期</text>
                <text class="info-value">{{ attendanceInfo.teachingDate }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">节次</text>
                <text class="info-value">{{ attendanceInfo.classPeriod }}节</text>
              </view>
              <view class="info-row">
                <text class="info-label">考勤状态</text>
                <text
                  class="info-value"
                  :class="getStatusClass(attendanceInfo.attendanceStatusName)"
                >
                  {{ attendanceInfo.attendanceStatusName }}
                </text>
              </view>
            </view>
          </view>

          <!-- 非出勤补课申请记录 -->
          <view class="info-card" v-if="attendanceInfo.apply">
            <view class="card-header">
              <text class="card-title">非出勤补课申请记录</text>
            </view>
            <view class="card-content">
              <view class="info-row">
                <text class="info-label">申请原因</text>
                <text class="info-value">{{ attendanceInfo.apply.sqsm }}</text>
              </view>
              <!-- 申请附件使用AttachmentList组件 -->
              <view v-if="attendanceInfo.apply.fjlb" class="attachment-section">
                <AttachmentList :attachment-urls="attendanceInfo.apply.fjlb" />
              </view>
              <view class="info-row">
                <text class="info-label">申请时间</text>
                <text class="info-value">{{ attendanceInfo.apply.sqsj }}</text>
              </view>
            </view>
          </view>

          <!-- 无申请记录提示 -->
          <view class="info-card" v-else>
            <view class="card-header">
              <text class="card-title">非出勤补课申请记录</text>
            </view>
            <view class="card-content">
              <view class="empty-state">
                <text class="empty-text">暂无申请记录</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
  </FormWithApproval>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import type { AttendanceItem, AttendanceApply } from '@/types/attendance'
import { useAttendanceStore } from '@/stores/attendance'
import AttachmentList from '@/components/workflow/AttachmentList.vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

const toast = useToast()
const attendanceStore = useAttendanceStore()

// 考勤信息
const attendanceInfo = ref<AttendanceItem>({} as AttendanceItem)

// 获取状态样式类
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    出勤: 'status-normal',
    迟到: 'status-late',
    请假: 'status-leave',
    旷课: 'status-absent',
    病假: 'status-sick',
    事假: 'status-personal',
  }
  return statusMap[status] || 'status-default'
}

onMounted(() => {
  // 从store获取考勤信息
  const currentAttendance = attendanceStore.getCurrentAttendance()
  if (currentAttendance) {
    attendanceInfo.value = currentAttendance
  } else {
    toast.error('考勤信息获取失败')
    uni.navigateBack()
  }
})
</script>

<style lang="scss" scoped>
.attendance-detail-page {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f7f8fa;
}

.detail-container {
  max-width: 750rpx;
  margin: 0 auto;
}

.info-card {
  margin-bottom: 20rpx;
  overflow: hidden;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  padding: 24rpx;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 24rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  flex-shrink: 0;
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

// 状态样式
.status-normal {
  color: #52c41a;
}

.status-late {
  color: #faad14;
}

.status-leave,
.status-sick,
.status-personal {
  color: #1890ff;
}

.status-absent {
  color: #ff4d4f;
}

.status-default {
  color: #666;
}

// 附件区域
.attachment-section {
  margin-top: 16rpx;
}

// 空状态
.empty-state {
  padding: 40rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
