import request from '@/utils/request'
import type {
  AttendanceQuery,
  AttendanceResponse,
  AttendanceApplyRequest,
} from '@/types/attendance'

/**
 * 获取学生考勤记录
 * @param params 查询参数
 * @returns 考勤记录
 */
export function getStudentAttendance(params: AttendanceQuery): Promise<AttendanceResponse> {
  return request('/student_server/student_attendance', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取考勤统计
 * @param semester 学期
 * @returns 考勤统计
 */
export function getAttendanceStatistics(semester: string): Promise<any> {
  return request('/student_server/attendance_statistics', {
    method: 'POST',
    data: {
      semester,
    },
  })
}

/**
 * 获取课程考勤
 * @param semester 学期
 * @returns 课程考勤列表
 */
export function getCourseAttendance(semester: string): Promise<any> {
  return request('/student_server/course_attendance', {
    method: 'POST',
    data: {
      semester,
    },
  })
}

/**
 * 获取班级排名
 * @param semester 学期
 * @returns 班级排名
 */
export function getClassRanking(semester: string): Promise<any> {
  return request('/student_server/class_ranking', {
    method: 'POST',
    data: {
      semester,
    },
  })
}

/**
 * 提交考勤申请
 * @param params 申请参数
 * @returns 申请结果
 */
export function submitAttendanceApply(params: AttendanceApplyRequest): Promise<boolean> {
  return request('/student_server/student_attendance/attendanceApply', {
    method: 'POST',
    data: params,
  })
}
