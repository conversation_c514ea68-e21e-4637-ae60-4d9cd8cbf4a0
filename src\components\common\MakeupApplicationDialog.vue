<template>
  <transition name="dialog-fade" appear>
    <view
      v-if="visible"
      class="fixed inset-0 z-999 flex items-center justify-center"
      @touchmove.prevent
    >
      <view class="absolute inset-0 bg-black bg-opacity-50" @click="handleCancel"></view>
      <view
        class="dialog-container relative flex flex-col w-90% max-w-[600rpx] overflow-hidden bg-white rounded-[12rpx] transition-all duration-300 transform"
      >
        <!-- 标题栏 -->
        <view
          class="flex items-center justify-between p-[24rpx] border-b border-b-solid border-[#eee]"
        >
          <text class="text-[32rpx] font-600 text-[#333]">非出勤补课申请</text>
          <wd-icon name="close" size="20px" class="text-[#999]" @click="handleCancel" />
        </view>

        <!-- 内容区域 -->
        <view class="p-[32rpx_24rpx]">
          <!-- 课程信息 -->
          <view class="course-info mb-[24rpx] p-[20rpx] bg-[#f8f9fa] rounded-[8rpx]">
            <view class="text-[30rpx] font-600 text-[#333] mb-[12rpx]">
              {{ courseInfo.courseName }} 补课
            </view>
            <view class="text-[26rpx] text-[#666] mb-[8rpx]">
              学生: {{ courseInfo.studentName }}({{ courseInfo.studentCode }})
            </view>
            <view class="text-[26rpx] text-[#666]">
              {{ courseInfo.teachingDate }} 第{{ courseInfo.classPeriod }}节
              {{ courseInfo.attendanceStatusName }} : {{ courseInfo.classHours }}节
            </view>
          </view>

          <!-- 申请原因 -->
          <view class="form-item mb-[24rpx]">
            <view class="text-[28rpx] font-500 text-[#333] mb-[12rpx]">
              申请原因
              <text class="text-[#ff4d4f]">*</text>
            </view>
            <TextareaInput
              v-model="formData.reason"
              placeholder="请输入申请原因..."
              :maxlength="200"
              :show-count="true"
              :auto-height="true"
            />
          </view>

          <!-- 上传附件 -->
          <view class="form-item">
            <view class="text-[28rpx] font-500 text-[#333] mb-[12rpx]">上传附件</view>
            <FileUploader
              v-model="formData.attachments"
              :show-title="false"
              tip-text="支持上传图片、文档等文件"
              empty-text="暂无附件"
            />
          </view>
        </view>

        <!-- 底部按钮 -->
        <view
          class="flex gap-[20rpx] justify-end p-[20rpx_24rpx] border-t border-t-solid border-[#eee]"
        >
          <ActionButton type="secondary" text="取消" @click="handleCancel" />
          <ActionButton
            type="primary"
            text="提交申请"
            @click="handleConfirm"
            :disabled="!formData.reason.trim()"
          />
        </view>
      </view>
    </view>
  </transition>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useToast } from 'wot-design-uni'
import type { AttendanceItem } from '@/types/attendance'
import ActionButton from './ActionButton.vue'
import TextareaInput from './TextareaInput.vue'
import FileUploader from './FileUploader.vue'

// Props定义
interface Props {
  /** 考勤记录信息 */
  attendanceItem?: AttendanceItem
}

// 事件定义
interface Emits {
  (e: 'confirm', data: { reason: string; attachments: any[]; attendanceItem: AttendanceItem }): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  attendanceItem: undefined,
})

const emit = defineEmits<Emits>()
const toast = useToast()

// 控制显示状态
const visible = ref(false)

// 当前考勤记录
const currentAttendanceItem = ref<AttendanceItem>()

// 表单数据
const formData = reactive({
  reason: '',
  attachments: [] as any[],
})

// 课程信息
const courseInfo = ref({
  courseName: '',
  studentName: '',
  studentCode: '',
  teachingDate: '',
  classPeriod: '',
  attendanceStatusName: '',
  classHours: 0,
})

// 显示对话框
const show = (attendanceItem?: AttendanceItem) => {
  const item = attendanceItem || props.attendanceItem
  if (item) {
    // 保存当前考勤记录
    currentAttendanceItem.value = item

    // 重置表单数据
    formData.reason = ''
    formData.attachments = []

    // 设置课程信息
    courseInfo.value = {
      courseName: item.courseName,
      studentName: item.studentName,
      studentCode: item.studentCode,
      teachingDate: item.teachingDate,
      classPeriod: item.classPeriod,
      attendanceStatusName: item.attendanceStatusName,
      classHours: item.classHours,
    }
  }
  visible.value = true
}

// 隐藏对话框
const hide = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  if (!formData.reason.trim()) {
    toast.error('请输入申请原因')
    return
  }

  if (!currentAttendanceItem.value) {
    toast.error('考勤记录信息缺失')
    return
  }

  hide()
  emit('confirm', {
    reason: formData.reason,
    attachments: formData.attachments,
    attendanceItem: currentAttendanceItem.value,
  })
}

// 处理取消
const handleCancel = () => {
  hide()
  emit('cancel')
}

// 对外暴露方法
defineExpose({
  show,
  hide,
})
</script>

<style lang="scss" scoped>
// 添加过渡动画样式
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
}

.dialog-fade-enter-active .dialog-container,
.dialog-fade-leave-active .dialog-container {
  transition: all 0.3s ease;
}

.dialog-fade-enter-from .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}

.dialog-fade-leave-to .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}
</style>
